{"openapi": "3.1.0", "info": {"title": "TMAI-TCamp-Rest API", "description": "TMAI-TCamp-Rest Backend API Service", "version": "1.0.0"}, "paths": {"/api/v1/sys/health": {"get": {"tags": ["sys"], "summary": "Health Check Api", "description": "健康检查接口\n\n此接口用于检查系统的运行状态，返回系统的基本信息，包括节点名称、启动时间、构建版本和环境信息。\n可用于监控系统、负载均衡健康检查或确认系统是否正常运行。\n\n返回:\n    dict: 包含系统状态信息的字典\n        - nodeName: 项目名称\n        - startTime: 系统启动时间\n        - buildVersion: 系统版本号\n        - env: 运行环境(如开发环境、测试环境、生产环境)", "operationId": "health_check_api_api_v1_sys_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/auth/login": {"post": {"tags": ["auth"], "summary": "<PERSON><PERSON>", "description": "用户登录\n\n## 功能描述\n用户使用用户名和密码进行身份验证登录。\n\n## 请求参数\n- **request** (LoginRequest): 登录请求信息，请求体\n    - username: 用户名\n    - password: 密码\n\n## 响应\n- **200**: 登录成功\n    - 返回类型: LoginResponse\n    - 包含访问令牌和令牌类型\n        - token: JWT访问令牌\n        - token_type: 令牌类型，固定为\"bearer\"\n        - uid: 用户ID\n        - username: 用户名\n        - name: 姓名\n        - tenant_id: 租户ID\n\n## 权限要求\n- 无需身份验证（这是登录接口）\n\n## 错误处理\n- **400**: 登录失败\n    - 用户名或密码错误时返回此错误", "operationId": "login_api_api_v1_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/logout": {"post": {"tags": ["auth"], "summary": "Logout Api", "description": "用户登出\n\n## 功能描述\n用户注销登录，使当前访问令牌失效。\n\n## 请求参数\n- 无需额外参数，通过Authorization header传递Bearer token\n\n## 响应\n- **200**: 登出成功\n    - 返回类型: LogoutResponse\n    - 包含登出成功的消息\n\n## 权限要求\n- 需要有效的用户身份令牌\n\n## 错误处理\n- **401**: 未授权访问，当令牌无效或过期时返回此错误", "operationId": "logout_api_api_v1_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogoutResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/auth/profile": {"get": {"tags": ["auth"], "summary": "Get Profile Api", "description": "获取用户个人信息\n\n## 功能描述\n获取当前登录用户的个人资料信息。\n\n## 请求参数\n- 无需额外参数，通过Authorization header传递Bearer token\n\n## 响应\n- **200**: 成功返回个人信息\n    - 返回类型: UserProfile\n    - 包含用户的个人资料信息\n        - uid: 用户ID\n        - username: 用户名\n        - name: 姓名\n        - tenant_id: 租户ID\n        - gender: 性别\n\n## 权限要求\n- 需要有效的用户身份令牌\n\n## 错误处理\n- **404**: 用户信息不存在\n    - 当用户记录在数据库中不存在时返回此错误", "operationId": "get_profile_api_api_v1_auth_profile_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/auth/password": {"put": {"tags": ["auth"], "summary": "Update Password Api", "description": "更新用户密码\n\n## 功能描述\n允许用户更新自己的登录密码。\n\n## 请求参数\n- **password_update** (UserPasswordUpdate): 密码更新信息，请求体\n    - old_password: 当前密码\n    - new_password: 新密码\n\n## 响应\n- **200**: 密码更新成功\n    - 返回成功消息\n\n## 权限要求\n- 需要有效的用户身份令牌\n\n## 错误处理\n- **400**: 密码更新失败\n    - 当前密码错误或用户不存在时返回此错误", "operationId": "update_password_api_api_v1_auth_password_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/class/classes": {"get": {"tags": ["class"], "summary": "Get Classes Api", "description": "获取当前用户所在班级列表\n\n## 功能描述\n获取当前登录用户（学员）所在的所有班级，按优先级从小到大排序，如果优先级相同按开始时间倒序排列。\n\n## 请求参数\n- 无需额外参数，通过Authorization header传递Bearer token\n\n## 响应\n- **200**: 成功返回班级列表\n    - 返回类型: ClassListResponse\n    - 包含班级列表信息\n        - classes: 班级列表\n            - id: 班级ID\n            - name: 班级名称\n            - pic: 班级图片URL（OSS签名URL）\n            - description: 班级描述\n            - btime: 开始时间\n            - etime: 结束时间\n\n## 权限要求\n- 需要有效的用户身份令牌\n\n## 错误处理\n- **401**: 未授权访问，当令牌无效或过期时返回此错误", "operationId": "get_classes_api_api_v1_class_classes_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassListResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/class/{id}/exercises": {"get": {"tags": ["class"], "summary": "Get Class Exercises Api", "description": "获取指定班级的所有练习信息\n\n## 功能描述\n获取指定班级的所有练习信息，包括练习详情和对应的老师信息，按优先级从小到大排序，如果优先级相同按创建时间倒序排列。\n\n## 请求参数\n- **id** (int): 班级ID，路径参数\n- 通过Authorization header传递Bearer token进行身份验证\n\n## 响应\n- **200**: 成功返回练习列表\n    - 返回类型: ClassExerciseListResponse\n    - 包含练习列表信息\n        - class_name: 班级名称\n        - exercises: 练习列表\n            - e_id: 练习ID\n            - e_title: 练习标题\n            - e_type: 练习类型（1：作业单；2：角色扮演）\n            - e_pic: 练习图片URL（OSS签名URL）\n            - e_intro: 练习简介\n            - e_duration: 练习时长（分钟）\n            - t_id: 老师ID\n            - t_name: 老师姓名\n            - t_avatar: 老师头像URL（OSS签名URL）\n            - t_intro: 老师简介\n            - depend: 练习依赖（0：不依赖；1：依赖）\n            - w_id: 作业单ID（如果存在）\n            - s_id: 场景ID（如果存在）\n            - el_id: 练习情况ID（如果存在）\n            - el_status: 练习状态（0：待练习；1：练习中；2：已提交；如果为null表示无练习记录“未开始”）\n            - el_btime: 开始练习时间\n            - el_stime: 提交练习时间\n            - el_utime: 上次更新时间\n\n## 权限要求\n- 需要有效的用户身份令牌\n\n## 错误处理\n- **401**: 未授权访问，当令牌无效或过期时返回此错误\n- **404**: 班级不存在或用户无权访问该班级", "operationId": "get_class_exercises_api_api_v1_class__id__exercises_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassExerciseListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/exercise/log": {"post": {"tags": ["exercise"], "summary": "Create Exercise Log Api", "description": "创建练习记录\n\n## 功能描述\n为某个练习创建练习记录，记录学员开始练习的时间和相关信息。\n如果发现满足tenant_id, cid, sid, eid条件的记录已经存在，则不创建新记录，直接返回已存在的记录。\n\n## 请求参数\n- **request** (ExerciseLogCreateRequest): 创建练习记录请求信息，请求体\n    - cid: 班级ID\n    - eid: 练习ID\n\n## 响应\n- **201 Created**: 新创建练习记录成功\n    - 返回类型: ExerciseLogCreateResponse\n    - 包含新创建的练习记录信息\n        - id: 练习情况ID\n        - btime: 开始练习时间\n- **200 OK**: 练习记录已存在\n    - 返回类型: ExerciseLogCreateResponse\n    - 包含已存在的练习记录信息\n        - id: 练习情况ID\n        - btime: 开始练习时间\n\n## 权限要求\n- 需要有效的用户身份令牌\n\n## 错误处理\n- **400**: 请求参数错误，班级练习关系或班级学员关系不存在\n- **401**: 未授权访问，当令牌无效或过期时返回此错误\n- **500**: 服务器内部错误", "operationId": "create_exercise_log_api_api_v1_exercise_log_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExerciseLogCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExerciseLogCreateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/exercise/log/{id}/status": {"put": {"tags": ["exercise"], "summary": "Update Exercise Log Status Api", "description": "更新练习记录状态\n\n## 功能描述\n更新指定的练习记录信息状态。\n\n## 请求参数\n- **id** (int): 练习情况ID，路径参数\n- **status** (int): 练习状态（0：待练习；1：练习中；2：已提交）\n\n## 响应\n- **200**: 更新成功\n    - 返回类型: MessageResponse\n    - 包含更新成功的消息\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能更新属于当前用户的练习记录\n\n## 错误处理\n- **400**: 请求参数错误，练习记录不存在、无权限访问或没有相关问题\n- **401**: 未授权访问，当令牌无效或过期时返回此错误\n- **404**: 练习记录不存在或无权限访问\n- **500**: 服务器内部错误", "operationId": "update_exercise_log_status_api_api_v1_exercise_log__id__status_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}, {"name": "status", "in": "query", "required": true, "schema": {"type": "integer", "title": "Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/worksheet/{id}": {"get": {"tags": ["worksheet"], "summary": "Get Worksheet Api", "description": "获取作业单基本信息\n\n## 功能描述\n获取指定作业单的基本信息，包括练习状态、时间信息等，但不包含单元列表。\n\n## 请求参数\n- **id** (int): 作业单ID，通过路径参数传递\n- **class_id** (int): 班级ID，通过查询参数传递\n\n## 响应\n- **200**: 成功返回作业单基本信息\n    - 返回类型: WorksheetBasicResponse\n    - 包含作业单的基本信息：\n        - title: 标题\n        - pic: 图片（OSS签名URL或null）\n        - intro: 简介\n        - duration: 时长（分钟）\n        - bgtext: 背景文字\n        - bgvideo: 背景视频URL（OSS签名URL或null）\n        - report: 整体点评报告URL（OSS签名URL或null）\n        - btime: 开始练习时间\n        - stime: 提交练习时间\n        - utime: 上次练习时间\n        - status: 练习状态（0：待练习；1：练习中；2：已提交）\n        - eid: 练习ID\n        - elid: 练习情况ID（可为null）\n        - tid: 老师ID（可为null）\n        - tname: 老师姓名（可为null）\n        - tavatar: 老师头像URL（OSS签名URL或null）\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能查看当前租户下的作业单\n\n## 业务规则\n- 只能访问当前租户下的作业单\n- 只能访问有效的（active=1）练习\n\n## 错误处理\n- **404**: 作业单不存在或无权访问\n    - 作业单ID不存在\n    - 无权访问该作业单（不属于当前租户）\n    - 关联的练习已被删除或无效", "operationId": "get_worksheet_api_api_v1_worksheet__id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "description": "作业单ID", "title": "Id"}, "description": "作业单ID"}, {"name": "class_id", "in": "query", "required": true, "schema": {"type": "integer", "description": "班级ID", "title": "Class Id"}, "description": "班级ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorksheetBasicResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/worksheet/{id}/units": {"get": {"tags": ["worksheet"], "summary": "Get Worksheet Units Api", "description": "获取作业单单元列表\n\n## 功能描述\n获取指定作业单的所有单元列表。\n\n## 请求参数\n- **id** (int): 作业单ID，通过路径参数传递\n\n## 响应\n- **200**: 成功返回单元列表\n    - 返回类型: WorksheetUnitsResponse\n    - 包含单元列表信息：\n        - unit_list: 单元列表（按priority从小到大排序）\n            - id: 单元ID\n            - name: 单元名称\n            - bgtext: 背景文字\n            - bgvideo: 背景视频URL（OSS签名URL或null）\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能查看当前租户下的作业单\n\n## 业务规则\n- 只能访问当前租户下的作业单\n- 单元按priority字段从小到大排序\n\n## 错误处理\n- **404**: 作业单不存在或无权访问\n    - 作业单ID不存在\n    - 无权访问该作业单（不属于当前租户）", "operationId": "get_worksheet_units_api_api_v1_worksheet__id__units_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "description": "作业单ID", "title": "Id"}, "description": "作业单ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorksheetUnitsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/worksheet/{wid}/unit/{uid}/questions": {"get": {"tags": ["worksheet"], "summary": "Get Unit Questions Api", "description": "获取作业单中单元下的所有问题信息\n\n## 功能描述\n获取指定作业单中指定单元下的所有问题信息，包括问题详情、理论框架、作答指南等。\n\n## 请求参数\n- **wid** (int): 作业单ID，通过路径参数传递\n- **uid** (int): 单元ID，通过路径参数传递\n- **elid** (int): 练习情况ID，通过查询参数传递\n\n## 响应\n- **200**: 成功返回单元问题列表\n    - 返回类型: UnitQuestionsResponse\n    - 包含问题的完整信息：\n        - question_list: 问题列表\n            - id: 问题ID\n            - title: 问题标题\n            - bgtext: 背景文字\n            - bgvideo: 背景视频URL（OSS签名URL或null）\n            - draft: 作答草稿\n            - answer: 已提交作答\n            - comment: AI点评\n            - framework_list: 理论框架列表（按priority从小到大排序）\n                - name: 理论框架名称\n                - logo: 理论框架logo（OSS签名URL或null）\n                - module_list: 理论模块列表（按priority从小到大排序）\n                    - name: 理论模块名称\n            - guide_list: 作答指南列表（按priority从小到大排序）\n                - title: 指南标题\n                - details: 指南详情\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能查看当前租户下的作业单和单元\n\n## 业务规则\n- 只能访问当前租户下的作业单和单元\n- 只能访问有效的（active=1）问题\n- 问题按worksheet_asm表中的priority字段从小到大排序\n- 理论框架按framework表中的priority字段从小到大排序\n- 理论模块按module表中的priority字段从小到大排序\n- 作答指南按question_guide表中的priority字段从小到大排序\n\n## 错误处理\n- **404**: 作业单或单元不存在或无权访问\n    - 作业单ID不存在\n    - 单元ID不存在\n    - 单元不属于指定的作业单\n    - 无权访问该作业单或单元（不属于当前租户）", "operationId": "get_unit_questions_api_api_v1_worksheet__wid__unit__uid__questions_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "wid", "in": "path", "required": true, "schema": {"type": "integer", "description": "作业单ID", "title": "Wid"}, "description": "作业单ID"}, {"name": "uid", "in": "path", "required": true, "schema": {"type": "integer", "description": "单元ID", "title": "<PERSON><PERSON>"}, "description": "单元ID"}, {"name": "elid", "in": "query", "required": true, "schema": {"type": "integer", "description": "练习情况ID", "title": "<PERSON><PERSON>"}, "description": "练习情况ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnitQuestionsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/worksheet/question/comment": {"post": {"tags": ["worksheet"], "summary": "Get Question Comment Api", "description": "获取AI老师对问题作答的流式点评\n\n## 功能描述\n根据问题ID和学员作答内容，调用AI服务获取老师点评，并将结果保存到数据库。\n\n## 请求参数\n- **request** (QuestionCommentRequest): 点评请求信息，请求体\n    - elid: 练习情况ID\n    - qid: 问题ID\n    - answer: 作答内容\n\n## 响应\n- **200**: 成功返回流式点评响应\n    - 返回类型: StreamingResponse\n    - 内容类型: text/event-stream\n    - 流式返回AI点评内容\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能对当前租户下的问题进行点评\n\n## 业务规则\n- 只能访问当前租户下的问题\n- 只能访问有效的（active=1）问题\n- 点评完成后自动保存到数据库\n\n## 错误处理\n- **404**: 问题不存在或无权访问\n    - 问题ID不存在\n    - 无权访问该问题（不属于当前租户）\n    - 问题已被删除或无效\n- **500**: 服务器内部错误\n    - AI服务配置错误\n    - 数据库操作失败", "operationId": "get_question_comment_api_api_v1_worksheet_question_comment_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionCommentRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/worksheet/question/retry": {"post": {"tags": ["worksheet"], "summary": "Retry Question Api", "description": "重新练习问题\n\n## 功能描述\n根据练习情况ID和问题ID，将该问题的已提交答案重置为草稿状态，清空已提交答案和AI点评。\n\n## 请求参数\n- **request** (QuestionRetryRequest): 重新练习请求信息，请求体\n    - elid: 练习情况ID\n    - qid: 问题ID\n\n## 响应\n- **200**: 成功重置问题状态\n    - 返回类型: dict\n    - 内容: {\"message\": \"重新练习成功\"}\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能对当前租户下的问题进行重新练习\n\n## 业务规则\n- 将问题的answer值复制到draft字段\n- 将answer和comment字段设为null\n- 只能操作当前租户下的记录\n\n## 错误处理\n- **404**: 问题记录不存在或无权访问\n    - 问题记录不存在\n    - 无权访问该问题记录（不属于当前租户）\n- **500**: 服务器内部错误\n    - 数据库操作失败", "operationId": "retry_question_api_api_v1_worksheet_question_retry_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionRetryRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/worksheet/question/answers": {"put": {"tags": ["worksheet"], "summary": "Batch Update Question Drafts Api", "description": "批量更新问题作答草稿\n\n## 功能描述\n批量更新指定练习情况下多个问题的作答草稿，并更新练习记录的最后更新时间。\n\n## 请求参数\n- **request** (QuestionDraftBatchRequest): 批量更新请求信息，请求体\n    - elid: 练习情况ID\n    - list: 问题草稿列表\n        - qid: 问题ID\n        - draft: 草稿内容\n\n## 响应\n- **200**: 成功批量更新草稿\n    - 返回类型: dict\n    - 内容: {\"message\": \"批量更新成功\"}\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能更新当前租户下的问题草稿\n\n## 业务规则\n- 批量更新tnt_worksheet_answer表中的draft字段\n- 同时更新tnt_exercise_log表的utime字段为当前时间\n- 两个表的更新操作在同一事务中执行\n- 如果记录不存在则创建新记录\n- 只能操作当前租户下的记录\n\n## 错误处理\n- **404**: 练习记录不存在或无权访问\n    - 练习情况ID不存在\n    - 无权访问该练习记录（不属于当前租户）\n- **500**: 服务器内部错误\n    - 数据库操作失败\n    - 事务回滚", "operationId": "batch_update_question_drafts_api_api_v1_worksheet_question_answers_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionDraftBatchRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/scene/{id}": {"get": {"tags": ["scene"], "summary": "Get Scene Api", "description": "获取场景基本信息\n\n## 功能描述\n获取指定场景的基本信息，包括练习状态、时间信息、角色列表、指南列表和发言列表等。\n\n## 请求参数\n- **id** (int): 场景ID，通过路径参数传递\n- **class_id** (int): 班级ID，通过查询参数传递\n\n## 响应\n- **200**: 成功返回场景基本信息\n    - 返回类型: SceneBasicResponse\n    - 包含场景的基本信息：\n        - title: 标题\n        - pic: 图片（OSS签名URL或null）\n        - intro: 简介\n        - duration: 时长（分钟）\n        - bgtext: 背景文字\n        - bgvideo: 背景视频URL（OSS签名URL或null）\n        - report: 整体点评报告URL（OSS签名URL或null）\n        - btime: 开始练习时间\n        - stime: 提交练习时间\n        - utime: 上次练习时间\n        - status: 练习状态（0：待练习；1：练习中；2：已提交）\n        - eid: 练习ID\n        - elid: 练习情况ID（可为null）\n        - tid: 老师ID（可为null）\n        - tname: 老师姓名（可为null）\n        - tavatar: 老师头像URL（OSS签名URL或null）\n        - characters: 角色列表\n            - id: 角色ID\n            - name: 姓名\n            - gender: 性别（0：未知；1：男；2：女）\n            - avatar: 头像URL（OSS签名URL或null）\n            - profile: 人物资料\n            - timbre_type: 音色类型（0：未设置；1：火山引擎）\n            - timbre: 音色\n            - played: 是否是学员扮演（0：否；1：是）\n        - guides: 指南列表（按priority从小到大排序）\n            - title: 指南标题\n            - details: 指南详情\n        - speeches: 场景练习情况（发言）列表（按发言时间排序）\n            - id: 发言ID\n            - cid: 角色ID\n            - played: 是否是学员扮演（0：否；1：是）\n            - content: 发言内容\n            - to_cids: @列表（角色ID列表，用逗号分隔，可为空字符串）\n            - status: 发言状态（0：未处理；1：处理中；2：已处理）\n            - ctime: 发言时间\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能查看当前租户下的场景\n\n## 业务规则\n- 只能访问当前租户下的场景\n- 只能访问有效的（active=1）练习\n- 角色按scene_character表中的priority字段从小到大排序\n- 指南按scene_guide表中的priority字段从小到大排序\n- 发言按发言时间从早到晚排序\n\n## 错误处理\n- **404**: 场景不存在或无权访问\n    - 场景ID不存在\n    - 无权访问该场景（不属于当前租户）\n    - 关联的练习已被删除或无效", "operationId": "get_scene_api_api_v1_scene__id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "description": "场景ID", "title": "Id"}, "description": "场景ID"}, {"name": "class_id", "in": "query", "required": true, "schema": {"type": "integer", "description": "班级ID", "title": "Class Id"}, "description": "班级ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SceneBasicResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/scene/speech": {"post": {"tags": ["scene"], "summary": "Create Speech Api", "description": "创建学员发言\n\n## 功能描述\n在场景练习中创建一条学员发言记录，记录到tnt_scene_speech表中。\n\n## 请求参数\n- **elid** (int): 练习情况ID\n- **cid** (int): 角色ID\n- **played** (int): 是否是学员扮演（0：否；1：是）\n- **content** (str): 发言内容\n- **to_cids** (str, optional): @列表（角色ID列表，用逗号分隔，可为空字符串）\n\n## 响应\n- **200**: 成功创建发言\n    - 返回类型: SceneSpeechCreateResponse\n    - 包含发言的基本信息：\n        - id: 发言ID\n        - status: 发言状态（0：未处理；1：处理中；2：已处理）\n        - elid: 练习情况ID\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能在自己的练习情况中创建发言\n- 只能使用当前租户下的角色\n\n## 业务规则\n- 练习情况必须存在且属于当前用户\n- 角色必须存在且属于当前租户\n- 发言状态默认为0（未处理）\n- 发言时间自动设置为当前时间\n\n## 错误处理\n- **404**: 练习情况不存在或无权访问\n    - 练习情况ID不存在\n    - 无权访问该练习情况（不属于当前用户）\n    - 关联的练习已被删除或无效\n- **400**: 角色不存在或无权使用\n    - 角色ID不存在\n    - 角色不属于当前租户\n    - 角色已被删除或无效\n- **500**: 服务器内部错误\n    - 数据库操作失败", "operationId": "create_speech_api_api_v1_scene_speech_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SceneSpeechRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SceneSpeechCreateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/scene/speech/{id}/jobs": {"post": {"tags": ["scene"], "summary": "Create Speech Jobs Api", "description": "创建发言任务\n\n## 功能描述\n根据发言ID和练习情况ID创建发言任务，通过AI分析发言内容并生成相应的任务列表。\n\n## 请求参数\n- **id** (int): 发言ID，通过路径参数传递\n- **elid** (int): 练习情况ID，通过请求体传递\n\n## 响应\n- **200**: 成功创建任务列表\n    - 返回类型: SceneSpeechJobsResponse\n    - 包含任务列表：\n        - jobs: 任务列表\n            - id: 任务ID\n            - cid: 完成任务的角色ID\n            - status: 任务状态（0：未开始；1：进行中；2：已完成）\n            - sync: 同步或异步（0：异步；1：同步）\n            - ctime: 创建时间\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能处理当前租户下的发言和练习情况\n\n## 业务规则\n1. 根据发言ID和练习情况ID查找未处理的发言记录（played=1且status=0）\n2. 更新发言状态为处理中（status=1）\n3. 采集相关数据：会议背景、会议记录、参会人员、调度规则、发言者信息\n4. 调用AI服务分析发言内容，获取角色分配结果\n5. 根据AI结果和调度规则创建任务记录\n6. 更新发言状态为已处理（status=2）并返回任务列表\n\n## 错误处理\n- **404**: 发言不存在或无权访问\n    - 发言ID不存在\n    - 发言不满足处理条件（played!=1或status!=0）\n    - 无权访问该发言（不属于当前租户）\n    - 关联的练习情况不存在\n- **500**: 服务器内部错误\n    - AI服务调用失败\n    - 数据库操作失败\n    - 机器人配置不存在\n\n## 注意事项\n- 如果处理过程中发生错误，会自动回滚发言状态到未处理（status=0）\n- 任务的同步/异步属性由调度规则中的serial字段决定\n- 只有在to_cids列表中的角色才会被分配任务（如果to_cids为空则不过滤）", "operationId": "create_speech_jobs_api_api_v1_scene_speech__id__jobs_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "description": "发言ID", "title": "Id"}, "description": "发言ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SceneSpeechJobRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SceneSpeechJobsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["scene"], "summary": "Get Speech Jobs Api", "description": "获取会议发言任务列表\n\n## 功能描述\n根据发言ID和练习情况ID获取会议发言任务列表。\n\n## 请求参数\n- **id** (int): 发言ID，通过路径参数传递\n- **elid** (int): 练习情况ID，通过查询参数传递\n\n## 响应\n- **200**: 成功返回任务列表\n    - 返回类型: SceneSpeechJobsResponse\n    - 包含任务列表：\n        - jobs: 任务列表\n            - id: 任务ID\n            - cid: 完成任务的角色ID\n            - status: 任务状态（0：未开始；1：进行中；2：已完成）\n            - sync: 同步或异步（0：异步；1：同步）\n            - ctime: 创建时间\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能查看当前租户下的任务\n\n## 业务规则\n1. 在tnt_scene_speech中根据id，elid条件以及played=1且status=0获取记录\n2. 如果找到记录，返回400错误（只能获得正确且未处理发言所对应的任务）\n3. 如果没有记录，从tnt_speech_job中根据elid获取job list（id从小到大排序）返回\n\n## 错误处理\n- **400**: 找到未处理的发言记录\n    - 存在played=1且status=0的发言记录\n    - 只能获得正确且未处理发言所对应的任务\n- **404**: 没有找到相关数据\n    - 练习情况不存在\n    - 无权访问该练习情况（不属于当前租户）\n- **500**: 服务器内部错误\n    - 数据库操作失败\n\n## 注意事项\n- 任务列表按ID从小到大排序\n- 只返回当前租户下的任务", "operationId": "get_speech_jobs_api_api_v1_scene_speech__id__jobs_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "description": "发言ID", "title": "Id"}, "description": "发言ID"}, {"name": "elid", "in": "query", "required": true, "schema": {"type": "integer", "description": "练习情况ID", "title": "<PERSON><PERSON>"}, "description": "练习情况ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SceneSpeechJobsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/scene/job/{id}/process": {"post": {"tags": ["scene"], "summary": "Process Job Api", "description": "处理任务\n\n## 功能描述\n根据任务ID处理发言任务，通过AI生成角色发言内容并创建新的发言记录。\n\n## 请求参数\n- **id** (int): 任务ID，通过路径参数传递\n\n## 响应\n- **200**: 成功返回流式响应\n    - 返回类型: StreamingResponse\n    - 内容类型: text/event-stream\n    - 流式返回AI生成的发言内容\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能处理当前租户下的任务\n\n## 业务规则\n1. 判断任务状态，只有status=0的任务才能处理\n2. 将任务状态更新为1（进行中）\n3. 根据任务中的发言ID获取原始发言内容\n4. 调用AI服务生成角色发言\n5. 成功后创建新的发言记录并删除任务\n6. 失败时将任务状态重置为0\n\n## 错误处理\n- **404**: 任务不存在或无权访问\n    - 任务ID不存在\n    - 无权访问该任务（不属于当前租户）\n- **400**: 任务状态不对无法处理\n    - 任务状态不为0（未开始）\n- **500**: 服务器内部错误\n    - AI服务调用失败\n    - 数据库操作失败\n    - 机器人配置不存在\n\n## 注意事项\n- 处理过程中如果发生错误，会自动回滚任务状态到未开始（status=0）\n- 成功处理后任务记录会被删除\n- 生成的发言记录played=0，status=2", "operationId": "process_job_api_api_v1_scene_job__id__process_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "description": "任务ID", "title": "Id"}, "description": "任务ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/scene/speech/batch": {"delete": {"tags": ["scene"], "summary": "Batch Delete Speeches Api", "description": "批量删除对话\n\n## 功能描述\n批量删除根据tenant_id和elid获取到的对话中，id大于等于from_id的所有数据。\n\n## 请求参数\n- **from_id** (int): 对话ID，删除ID大于等于此值的所有对话\n- **elid** (int): 练习情况ID\n\n## 响应\n- **200**: 成功删除对话\n    - 返回类型: SceneSpeechBatchDeleteResponse\n    - 包含删除的对话数量：\n        - deleted_count: 删除对话的数量\n\n## 权限要求\n- 需要有效的用户身份令牌\n- 只能删除当前租户下的对话\n\n## 业务规则\n- 批量删除根据tenant_id和elid获取到的对话中，id大于等于from_id的所有数据\n- 在同一个事务中完成删除操作\n- 只能删除当前租户下的对话\n\n## 错误处理\n- **500**: 服务器内部错误\n    - 数据库操作失败\n\n## 注意事项\n- 删除操作在事务中进行，确保数据一致性\n- 返回实际删除的对话数量", "operationId": "batch_delete_speeches_api_api_v1_scene_speech_batch_delete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SceneSpeechBatchDeleteRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SceneSpeechBatchDeleteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/misc/stt/wsurl": {"get": {"tags": ["misc"], "summary": "Get Speech To Text Ws Url", "description": "获取语音听写 WebSocket URL\n\n## 功能描述\n该接口返回用于语音听写的 WebSocket URL，客户端可以通过该 URL 连接到语音听写服务。\n\n## 请求参数\n无需额外参数，仅需要有效的用户身份令牌。\n\n## 响应\n- **200**: 成功返回语音听写 WebSocket URL\n    - 返回类型: WebSocketUrlResponse\n    - 包含以下字段：\n        - wsUrl: WebSocket连接URL（包含鉴权参数）\n        - appId: 语音听写应用ID\n\n## 权限要求\n- 需要有效的用户身份令牌\n\n## 业务规则\n- 根据讯飞开放平台的规则生成带鉴权参数的 WebSocket URL\n- URL包含必要的鉴权信息，客户端可直接使用\n- 生成的URL具有时效性，建议及时使用\n\n## 错误处理\n- **500**: 服务器内部错误\n    - 语音听写服务配置错误\n    - 鉴权参数生成失败", "operationId": "get_speech_to_text_ws_url_api_v1_misc_stt_wsurl_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebSocketUrlResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/tts/stream": {"post": {"tags": ["tts"], "summary": "Tts Stream Endpoint", "description": "TTS流式HTTP端点（备用方案）\n\n## 功能描述\n提供基于HTTP的流式语音合成服务，作为WebSocket的备用方案。\n\n## 请求参数\n- **text** (str): 要合成的文本内容\n- **timbre** (str): 音色类型\n- **speed_ratio** (float, optional): 语速比例，默认1.0\n- **volume_ratio** (float, optional): 音量比例，默认1.0\n- **pitch_ratio** (float, optional): 音调比例，默认1.0\n- **encoding** (str, optional): 音频编码格式，默认mp3\n\n## 响应\n- **200**: 成功返回音频流\n    - 返回类型: StreamingResponse\n    - 内容类型: audio/mpeg (mp3) 或其他音频格式\n\n## 错误处理\n- **400**: 请求参数错误\n- **500**: TTS服务错误", "operationId": "tts_stream_endpoint_api_v1_tts_stream_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TTSWebSocketRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tts/ws": {"get": {"tags": ["tts"], "summary": "TTS WebSocket 端点", "description": "\n## 功能描述\n提供流式语音合成服务，客户端通过WebSocket连接发送文本，服务器返回合成的音频流。\n\n## 连接信息\n- **协议**: WebSocket\n- **URL**: `ws://localhost:8000/api/v1/tts/ws`\n- **认证要求**: 必须提供有效的JWT token\n\n## 认证方式\n支持两种认证方式：\n1. **查询参数**: `ws://localhost:8000/api/v1/tts/ws?token=YOUR_JWT_TOKEN`\n2. **Authorization header**: `Authorization: Bearer YOUR_JWT_TOKEN`\n\n## 连接流程\n1. 客户端建立WebSocket连接（需要提供有效token）\n2. 服务器验证用户身份\n3. 发送JSON格式的TTS请求\n4. 服务器返回确认响应\n5. 服务器流式返回音频数据\n6. 服务器发送完成响应\n7. 连接结束\n\n## 请求格式\n客户端需要发送JSON格式的请求：\n```json\n{\n    \"text\": \"要合成的文本内容\",\n    \"timbre\": \"音色类型\",\n    \"speed_ratio\": 1.0,\n    \"volume_ratio\": 1.0,\n    \"pitch_ratio\": 1.0,\n    \"encoding\": \"mp3\"\n}\n```\n\n## 响应格式\n### 1. 确认响应（JSON）\n```json\n{\n    \"success\": true,\n    \"message\": \"Request received, starting TTS processing\",\n    \"request_id\": null\n}\n```\n\n### 2. 音频数据流（二进制）\n服务器会流式发送音频二进制数据块\n\n### 3. 完成响应（JSON）\n```json\n{\n    \"success\": true,\n    \"message\": \"TTS processing completed\",\n    \"request_id\": null\n}\n```\n\n### 4. 错误响应（JSON）\n```json\n{\n    \"error_code\": 400,\n    \"error_message\": \"错误描述\",\n    \"request_id\": null\n}\n```\n\n## 错误处理\n- **认证失败**: 拒绝连接（code: 1008）\n- **连接断开**: 自动清理资源\n- **参数错误**: 返回错误信息并关闭连接（code: 1003）\n- **TTS服务错误**: 返回错误信息并关闭连接（code: 1011）\n\n## 使用示例\n```javascript\n// 方式1: 通过查询参数传递token\nconst token = 'YOUR_JWT_TOKEN';\nconst ws = new WebSocket(`ws://localhost:8000/api/v1/tts/ws?token=${token}`);\n\n// 方式2: 通过Authorization header（需要支持的WebSocket客户端）\nconst ws = new WebSocket('ws://localhost:8000/api/v1/tts/ws', [], {\n    headers: {\n        'Authorization': `Bearer ${token}`\n    }\n});\n\nws.onopen = function() {\n    // 发送TTS请求\n    ws.send(JSON.stringify({\n        text: \"你好，这是一个测试\",\n        timbre: \"zh_female_shuangkuaisisi_moon_bigtts\",\n        speed_ratio: 1.0,\n        volume_ratio: 1.0,\n        pitch_ratio: 1.0,\n        encoding: \"mp3\"\n    }));\n};\n\nws.onmessage = function(event) {\n    if (event.data instanceof Blob) {\n        // 音频数据\n        console.log('Received audio data:', event.data);\n    } else {\n        // JSON响应\n        const response = JSON.parse(event.data);\n        console.log('Received response:', response);\n    }\n};\n\nws.onclose = function(event) {\n    if (event.code === 1008) {\n        console.error('认证失败，请检查token是否有效');\n    }\n};\n```\n            ", "parameters": [{"name": "token", "in": "query", "required": false, "schema": {"type": "string"}, "description": "JWT认证token（可选，也可通过Authorization header传递）"}, {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer JWT token（格式：Bearer YOUR_JWT_TOKEN）"}, {"name": "Upgrade", "in": "header", "required": true, "schema": {"type": "string", "enum": ["websocket"]}, "description": "必须设置为 'websocket'"}, {"name": "Connection", "in": "header", "required": true, "schema": {"type": "string", "enum": ["Upgrade"]}, "description": "必须设置为 'Upgrade'"}, {"name": "Sec-WebSocket-Key", "in": "header", "required": true, "schema": {"type": "string"}, "description": "WebSocket握手密钥"}, {"name": "Sec-WebSocket-Version", "in": "header", "required": true, "schema": {"type": "string", "enum": ["13"]}, "description": "WebSocket版本，必须为13"}], "responses": {"101": {"description": "WebSocket连接建立成功", "headers": {"Upgrade": {"schema": {"type": "string"}, "description": "websocket"}, "Connection": {"schema": {"type": "string"}, "description": "Upgrade"}, "Sec-WebSocket-Accept": {"schema": {"type": "string"}, "description": "WebSocket接受密钥"}}}, "400": {"description": "WebSocket握手失败或请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TTSErrorResponse"}}}}, "401": {"description": "认证失败 - 未提供token或token无效", "content": {"text/plain": {"schema": {"type": "string", "example": "Authentication failed"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TTSErrorResponse"}}}}}, "security": [{"JWT": []}]}}}, "components": {"schemas": {"ClassExerciseListResponse": {"properties": {"class_name": {"type": "string", "title": "Class Name", "description": "班级名称"}, "exercises": {"items": {"$ref": "#/components/schemas/ClassExerciseResponse"}, "type": "array", "title": "Exercises", "description": "练习列表"}}, "type": "object", "required": ["class_name", "exercises"], "title": "ClassExerciseListResponse", "description": "班级练习列表响应"}, "ClassExerciseResponse": {"properties": {"e_id": {"type": "integer", "title": "E Id", "description": "练习ID"}, "e_title": {"type": "string", "title": "E Title", "description": "练习标题"}, "e_type": {"type": "integer", "title": "E Type", "description": "练习类型"}, "e_pic": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "E Pic", "description": "练习图片URL"}, "e_intro": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "E Intro", "description": "练习简介"}, "e_duration": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "E Duration", "description": "练习时长（分钟）"}, "t_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "T Id", "description": "老师ID"}, "t_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "T Name", "description": "老师姓名"}, "t_avatar": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "T Avatar", "description": "老师头像URL"}, "t_intro": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "T Intro", "description": "老师简介"}, "depend": {"type": "integer", "title": "Depend", "description": "练习依赖"}, "w_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "W Id", "description": "作业单ID"}, "s_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "S Id", "description": "场景ID"}, "el_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "El Id", "description": "练习情况ID"}, "el_status": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "El Status", "description": "练习状态（0：待练习；1：练习中；2：已提交）"}, "el_btime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "El Btime", "description": "开始练习时间"}, "el_stime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "El Stime", "description": "提交练习时间"}, "el_utime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "El Utime", "description": "上次更新时间"}}, "type": "object", "required": ["e_id", "e_title", "e_type", "depend"], "title": "ClassExerciseResponse", "description": "班级练习信息响应"}, "ClassListResponse": {"properties": {"classes": {"items": {"$ref": "#/components/schemas/ClassResponse"}, "type": "array", "title": "Classes", "description": "班级列表"}}, "type": "object", "required": ["classes"], "title": "ClassListResponse", "description": "班级列表响应"}, "ClassResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "班级ID"}, "name": {"type": "string", "title": "Name", "description": "班级名称"}, "pic": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pic", "description": "班级图片URL"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "班级描述"}, "btime": {"type": "string", "format": "date-time", "title": "Btime", "description": "开始时间"}, "etime": {"type": "string", "format": "date-time", "title": "Etime", "description": "结束时间"}}, "type": "object", "required": ["id", "name", "btime", "etime"], "title": "ClassResponse", "description": "班级信息响应"}, "ExerciseLogCreateRequest": {"properties": {"cid": {"type": "integer", "title": "Cid", "description": "班级ID"}, "eid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "练习ID"}}, "type": "object", "required": ["cid", "eid"], "title": "ExerciseLogCreateRequest", "description": "创建练习记录请求"}, "ExerciseLogCreateResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "练习情况ID"}, "btime": {"type": "string", "format": "date-time", "title": "Btime", "description": "开始练习时间"}}, "type": "object", "required": ["id", "btime"], "title": "ExerciseLogCreateResponse", "description": "创建练习记录响应"}, "FrameworkModuleResponse": {"properties": {"name": {"type": "string", "title": "Name", "description": "理论模块名称"}}, "type": "object", "required": ["name"], "title": "FrameworkModuleResponse", "description": "理论模块响应"}, "FrameworkResponse": {"properties": {"name": {"type": "string", "title": "Name", "description": "理论框架名称"}, "logo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Logo", "description": "理论框架logo URL"}, "module_list": {"items": {"$ref": "#/components/schemas/FrameworkModuleResponse"}, "type": "array", "title": "Module List", "description": "理论模块列表"}}, "type": "object", "required": ["name"], "title": "FrameworkResponse", "description": "理论框架响应"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "LoginRequest": {"properties": {"username": {"type": "string", "title": "Username", "description": "用户名"}, "password": {"type": "string", "title": "Password", "description": "密码"}}, "type": "object", "required": ["username", "password"], "title": "LoginRequest", "description": "登录请求"}, "LoginResponse": {"properties": {"token": {"type": "string", "title": "Token", "description": "访问令牌"}, "token_type": {"type": "string", "title": "Token Type", "description": "令牌类型", "default": "bearer"}, "uid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "用户ID"}, "username": {"type": "string", "title": "Username", "description": "用户名"}, "name": {"type": "string", "title": "Name", "description": "姓名"}, "tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}}, "type": "object", "required": ["token", "uid", "username", "name", "tenant_id"], "title": "LoginResponse", "description": "登录响应"}, "LogoutResponse": {"properties": {"message": {"type": "string", "title": "Message", "description": "响应消息", "default": "登出成功"}}, "type": "object", "title": "LogoutResponse", "description": "登出响应"}, "MessageResponse": {"properties": {"message": {"type": "string", "title": "Message", "description": "响应消息"}}, "type": "object", "required": ["message"], "title": "MessageResponse", "description": "消息响应Schema类"}, "PasswordUpdateRequest": {"properties": {"old_password": {"type": "string", "title": "Old Password", "description": "旧密码"}, "new_password": {"type": "string", "title": "New Password", "description": "新密码"}}, "type": "object", "required": ["old_password", "new_password"], "title": "PasswordUpdateRequest", "description": "密码更新请求"}, "ProfileResponse": {"properties": {"uid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "用户ID"}, "username": {"type": "string", "title": "Username", "description": "用户名"}, "name": {"type": "string", "title": "Name", "description": "姓名"}, "tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "gender": {"type": "integer", "title": "Gender", "description": "性别（0：未知；1：男；2：女）"}}, "type": "object", "required": ["uid", "username", "name", "tenant_id", "gender"], "title": "ProfileResponse", "description": "用户个人信息响应"}, "QuestionCommentRequest": {"properties": {"elid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "练习情况ID"}, "qid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "问题ID"}, "answer": {"type": "string", "title": "Answer", "description": "作答内容"}}, "type": "object", "required": ["elid", "qid", "answer"], "title": "QuestionCommentRequest", "description": "问题点评请求"}, "QuestionDraftBatchRequest": {"properties": {"elid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "练习情况ID"}, "list": {"items": {"$ref": "#/components/schemas/QuestionDraftItem"}, "type": "array", "title": "List", "description": "问题草稿列表"}}, "type": "object", "required": ["elid", "list"], "title": "QuestionDraftBatchRequest", "description": "批量更新问题草稿请求"}, "QuestionDraftItem": {"properties": {"qid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "问题ID"}, "draft": {"type": "string", "title": "Draft", "description": "草稿"}}, "type": "object", "required": ["qid", "draft"], "title": "QuestionDraftItem", "description": "问题草稿项"}, "QuestionGuideResponse": {"properties": {"title": {"type": "string", "title": "Title", "description": "指南标题"}, "details": {"type": "string", "title": "Details", "description": "指南详情"}}, "type": "object", "required": ["title", "details"], "title": "QuestionGuideResponse", "description": "问题作答指南响应"}, "QuestionRetryRequest": {"properties": {"elid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "练习情况ID"}, "qid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "问题ID"}}, "type": "object", "required": ["elid", "qid"], "title": "QuestionRetryRequest", "description": "问题重新练习请求"}, "SceneBasicResponse": {"properties": {"title": {"type": "string", "title": "Title", "description": "标题"}, "pic": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pic", "description": "图片URL"}, "intro": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Intro", "description": "简介"}, "duration": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration", "description": "时长（分钟）"}, "bgtext": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bgtext", "description": "背景文字"}, "bgvideo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bgvideo", "description": "背景视频URL"}, "report": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Report", "description": "整体点评报告URL"}, "btime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Btime", "description": "开始练习时间"}, "stime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Stime", "description": "提交练习时间"}, "utime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Utime", "description": "上次练习时间"}, "status": {"type": "integer", "title": "Status", "description": "练习状态（0：待练习；1：练习中；2：已提交）"}, "eid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "练习ID"}, "elid": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "练习情况ID"}, "tid": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tid", "description": "老师ID"}, "tname": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tname", "description": "老师姓名"}, "tavatar": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "老师头像URL"}, "characters": {"items": {"$ref": "#/components/schemas/SceneCharacterResponse"}, "type": "array", "title": "Characters", "description": "角色列表"}, "guides": {"items": {"$ref": "#/components/schemas/SceneGuideResponse"}, "type": "array", "title": "Guides", "description": "指南列表"}, "speeches": {"items": {"$ref": "#/components/schemas/SceneSpeechResponse"}, "type": "array", "title": "Speeches", "description": "场景练习情况（发言）列表"}}, "type": "object", "required": ["title", "status", "eid"], "title": "SceneBasicResponse", "description": "场景基本信息响应"}, "SceneCharacterResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "角色ID"}, "name": {"type": "string", "title": "Name", "description": "姓名"}, "gender": {"type": "integer", "title": "Gender", "description": "性别（0：未知；1：男；2：女）"}, "avatar": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar", "description": "头像URL"}, "profile": {"type": "string", "title": "Profile", "description": "人物资料"}, "timbre_type": {"type": "integer", "title": "Timbre Type", "description": "音色类型（0：未设置；1：火山引擎）"}, "timbre": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timbre", "description": "音色"}, "played": {"type": "integer", "title": "Played", "description": "是否是学员扮演（0：否；1：是）"}}, "type": "object", "required": ["id", "name", "gender", "profile", "timbre_type", "played"], "title": "SceneCharacterResponse", "description": "场景角色响应"}, "SceneGuideResponse": {"properties": {"title": {"type": "string", "title": "Title", "description": "指南标题"}, "details": {"type": "string", "title": "Details", "description": "指南详情"}}, "type": "object", "required": ["title", "details"], "title": "SceneGuideResponse", "description": "场景指南响应"}, "SceneSpeechBatchDeleteRequest": {"properties": {"from_id": {"type": "integer", "title": "From Id", "description": "对话ID"}, "elid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "练习情况ID"}}, "type": "object", "required": ["from_id", "elid"], "title": "SceneSpeechBatchDeleteRequest", "description": "批量删除对话请求"}, "SceneSpeechBatchDeleteResponse": {"properties": {"deleted_count": {"type": "integer", "title": "Deleted Count", "description": "删除对话的数量"}}, "type": "object", "required": ["deleted_count"], "title": "SceneSpeechBatchDeleteResponse", "description": "批量删除对话响应"}, "SceneSpeechCreateResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "发言ID"}, "status": {"type": "integer", "title": "Status", "description": "发言状态"}, "elid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "练习情况ID"}}, "type": "object", "required": ["id", "status", "elid"], "title": "SceneSpeechCreateResponse", "description": "场景发言创建响应"}, "SceneSpeechJobRequest": {"properties": {"elid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "练习情况ID"}}, "type": "object", "required": ["elid"], "title": "SceneSpeechJobRequest", "description": "场景发言任务请求"}, "SceneSpeechJobResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "任务ID"}, "cid": {"type": "integer", "title": "Cid", "description": "完成任务的角色ID"}, "status": {"type": "integer", "title": "Status", "description": "任务状态"}, "sync": {"type": "integer", "title": "Sync", "description": "同步或异步（0:异步；1:同步；）"}, "ctime": {"type": "string", "format": "date-time", "title": "Ctime", "description": "发言时间"}}, "type": "object", "required": ["id", "cid", "status", "sync", "ctime"], "title": "SceneSpeechJobResponse", "description": "场景发言任务响应"}, "SceneSpeechJobsResponse": {"properties": {"jobs": {"items": {"$ref": "#/components/schemas/SceneSpeechJobResponse"}, "type": "array", "title": "Jobs", "description": "任务列表"}}, "type": "object", "title": "SceneSpeechJobsResponse", "description": "场景发言任务列表响应"}, "SceneSpeechRequest": {"properties": {"elid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "练习情况ID"}, "cid": {"type": "integer", "title": "Cid", "description": "角色ID"}, "played": {"type": "integer", "title": "Played", "description": "是否是学员扮演（0：否；1：是）"}, "content": {"type": "string", "title": "Content", "description": "发言内容"}, "to_cids": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To Cids", "description": "@列表（角色ID列表，用逗号分隔，可为空字符串）", "default": ""}}, "type": "object", "required": ["elid", "cid", "played", "content"], "title": "SceneSpeechRequest", "description": "场景发言请求"}, "SceneSpeechResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "发言ID"}, "cid": {"type": "integer", "title": "Cid", "description": "角色ID"}, "played": {"type": "integer", "title": "Played", "description": "是否是学员扮演（0：否；1：是）"}, "content": {"type": "string", "title": "Content", "description": "发言内容"}, "to_cids": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To Cids", "description": "@列表（角色ID列表，用逗号分隔，可为空字符串）"}, "status": {"type": "integer", "title": "Status", "description": "发言状态（0：未处理；1：处理中；2：已处理）"}, "ctime": {"type": "string", "format": "date-time", "title": "Ctime", "description": "发言时间"}}, "type": "object", "required": ["id", "cid", "played", "content", "status", "ctime"], "title": "SceneSpeechResponse", "description": "场景发言响应"}, "TTSWebSocketRequest": {"type": "object", "required": ["text", "timbre"], "properties": {"text": {"type": "string", "description": "要合成的文本内容", "minLength": 1, "maxLength": 5000, "example": "你好，这是一个测试"}, "timbre": {"type": "string", "description": "音色类型", "minLength": 1, "example": "zh_female_shuang<PERSON><PERSON><PERSON>_moon_bigtts"}, "speed_ratio": {"type": "number", "description": "语速比例", "minimum": 0.5, "maximum": 2.0, "default": 1.0, "example": 1.0}, "volume_ratio": {"type": "number", "description": "音量比例", "minimum": 0.5, "maximum": 2.0, "default": 1.0, "example": 1.0}, "pitch_ratio": {"type": "number", "description": "音调比例", "minimum": 0.5, "maximum": 2.0, "default": 1.0, "example": 1.0}, "encoding": {"type": "string", "description": "音频编码格式", "default": "mp3", "example": "mp3"}}, "example": {"text": "你好，这是一个测试", "timbre": "zh_female_shuang<PERSON><PERSON><PERSON>_moon_bigtts", "speed_ratio": 1.0, "volume_ratio": 1.0, "pitch_ratio": 1.0, "encoding": "mp3"}}, "UnitQuestionResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "问题ID"}, "title": {"type": "string", "title": "Title", "description": "问题标题"}, "bgtext": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bgtext", "description": "背景文字"}, "bgvideo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bgvideo", "description": "背景视频URL"}, "draft": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Draft", "description": "作答草稿"}, "answer": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Answer", "description": "已提交作答"}, "comment": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Comment", "description": "AI点评"}, "framework_list": {"items": {"$ref": "#/components/schemas/FrameworkResponse"}, "type": "array", "title": "Framework List", "description": "理论框架列表"}, "guide_list": {"items": {"$ref": "#/components/schemas/QuestionGuideResponse"}, "type": "array", "title": "Guide List", "description": "作答指南列表"}}, "type": "object", "required": ["id", "title"], "title": "UnitQuestionResponse", "description": "单元问题响应"}, "UnitQuestionsResponse": {"properties": {"question_list": {"items": {"$ref": "#/components/schemas/UnitQuestionResponse"}, "type": "array", "title": "Question List", "description": "问题列表"}}, "type": "object", "title": "UnitQuestionsResponse", "description": "单元问题列表响应"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "WebSocketUrlResponse": {"properties": {"wsUrl": {"type": "string", "title": "Wsurl"}, "appId": {"type": "string", "title": "App<PERSON>"}}, "type": "object", "required": ["wsUrl", "appId"], "title": "WebSocketUrlResponse", "description": "语音听写 WebSocket URL 响应模型"}, "WorksheetBasicResponse": {"properties": {"title": {"type": "string", "title": "Title", "description": "标题"}, "pic": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pic", "description": "图片URL"}, "intro": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Intro", "description": "简介"}, "duration": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration", "description": "时长（分钟）"}, "bgtext": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bgtext", "description": "背景文字"}, "bgvideo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bgvideo", "description": "背景视频URL"}, "report": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Report", "description": "整体点评报告URL"}, "btime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Btime", "description": "开始练习时间"}, "stime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Stime", "description": "提交练习时间"}, "utime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Utime", "description": "上次练习时间"}, "status": {"type": "integer", "title": "Status", "description": "练习状态（0：待练习；1：练习中；2：已提交）"}, "eid": {"type": "integer", "title": "<PERSON><PERSON>", "description": "练习ID"}, "elid": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "练习情况ID"}, "tid": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tid", "description": "老师ID"}, "tname": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tname", "description": "老师姓名"}, "tavatar": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "老师头像URL"}}, "type": "object", "required": ["title", "status", "eid"], "title": "WorksheetBasicResponse", "description": "作业单基本信息响应"}, "WorksheetUnitResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "单元ID"}, "name": {"type": "string", "title": "Name", "description": "单元名称"}, "bgtext": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bgtext", "description": "背景文字"}, "bgvideo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bgvideo", "description": "背景视频URL"}}, "type": "object", "required": ["id", "name"], "title": "WorksheetUnitResponse", "description": "作业单单元响应"}, "WorksheetUnitsResponse": {"properties": {"unit_list": {"items": {"$ref": "#/components/schemas/WorksheetUnitResponse"}, "type": "array", "title": "Unit List", "description": "单元列表"}}, "type": "object", "title": "WorksheetUnitsResponse", "description": "作业单单元列表响应"}, "TTSWebSocketResponse": {"type": "object", "required": ["success", "message"], "properties": {"success": {"type": "boolean", "description": "请求是否成功"}, "message": {"type": "string", "description": "响应消息"}, "request_id": {"type": "string", "description": "请求ID", "nullable": true}}, "example": {"success": true, "message": "Request received, starting TTS processing", "request_id": null}}, "TTSErrorResponse": {"type": "object", "required": ["error_code", "error_message"], "properties": {"error_code": {"type": "integer", "description": "错误代码"}, "error_message": {"type": "string", "description": "错误消息"}, "request_id": {"type": "string", "description": "请求ID", "nullable": true}}, "example": {"error_code": 400, "error_message": "Invalid request format", "request_id": null}}}, "securitySchemes": {"JWT": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Enter JWT Bearer token"}}}}